#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Test script to verify AIMX prerequisites are properly installed

.DESCRIPTION
    This script checks if the prerequisites installed by Pre-Deploy-AIMX.ps1 are working correctly:
    1. Verifies .NET 8.0 Runtime installation
    2. Checks strong name verification bypass status
    3. Tests system compatibility for AIMX

.EXAMPLE
    .\Test-Prerequisites.ps1

.NOTES
    This script can be run without Administrator privileges for read-only checks.

#>

[CmdletBinding()]
param()

# Function to test .NET 8.0 installation
function Test-DotNetInstallation {
    Write-Host "=== Testing .NET 8.0 Installation ===" -ForegroundColor Magenta
    
    try {
        # Test dotnet command availability
        $dotnetVersion = & dotnet --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ dotnet command is available (version: $dotnetVersion)" -ForegroundColor Green
            
            # List all runtimes
            $runtimes = & dotnet --list-runtimes 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Installed .NET Runtimes:" -ForegroundColor Green
                $runtimes | ForEach-Object {
                    if ($_ -match "Microsoft\.NETCore\.App 8\.") {
                        Write-Host "  ✓ $_" -ForegroundColor Green
                    } elseif ($_ -match "Microsoft\.AspNetCore\.App 8\.") {
                        Write-Host "  ✓ $_" -ForegroundColor Green
                    } else {
                        Write-Host "  • $_" -ForegroundColor Gray
                    }
                }
                
                # Check specifically for .NET 8.0
                $net8Core = $runtimes | Where-Object { $_ -match "Microsoft\.NETCore\.App 8\." }
                $net8AspNet = $runtimes | Where-Object { $_ -match "Microsoft\.AspNetCore\.App 8\." }
                
                if ($net8Core) {
                    Write-Host "✓ .NET 8.0 Core Runtime is installed" -ForegroundColor Green
                } else {
                    Write-Host "✗ .NET 8.0 Core Runtime is missing" -ForegroundColor Red
                }
                
                if ($net8AspNet) {
                    Write-Host "✓ ASP.NET Core 8.0 Runtime is installed" -ForegroundColor Green
                } else {
                    Write-Host "✗ ASP.NET Core 8.0 Runtime is missing" -ForegroundColor Red
                }
                
                return ($net8Core -and $net8AspNet)
            } else {
                Write-Host "✗ Failed to list .NET runtimes" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "✗ dotnet command is not available or not working" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ Error testing .NET installation: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to test strong name verification bypass
function Test-StrongNameBypass {
    Write-Host "=== Testing Strong Name Verification Bypass ===" -ForegroundColor Magenta
    
    try {
        # Check if sn.exe is available
        $snPath = Get-Command sn.exe -ErrorAction SilentlyContinue
        if ($snPath) {
            Write-Host "✓ sn.exe is available at: $($snPath.Source)" -ForegroundColor Green
            
            # Try to list verification skip entries
            $skipList = & sn.exe -Vl 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Strong name verification skip list:" -ForegroundColor Green
                
                $aimxAssemblies = @("AimxShared", "NetRagService", "Aimx.ServiceInstaller", "DatabaseConverter", "IntentPlanningService")
                $foundBypass = $false
                
                foreach ($assembly in $aimxAssemblies) {
                    if ($skipList -match $assembly) {
                        Write-Host "  ✓ $assembly - bypass configured" -ForegroundColor Green
                        $foundBypass = $true
                    } else {
                        Write-Host "  • $assembly - no bypass found" -ForegroundColor Gray
                    }
                }
                
                if ($foundBypass) {
                    Write-Host "✓ Strong name verification bypass is configured for some AIMX assemblies" -ForegroundColor Green
                } else {
                    Write-Host "• No AIMX-specific strong name bypasses found (may not be needed)" -ForegroundColor Yellow
                }
                
                return $true
            } else {
                Write-Host "• Could not retrieve strong name verification skip list" -ForegroundColor Yellow
                return $true  # Not a critical failure
            }
        } else {
            Write-Host "• sn.exe not found (strong name bypass may not be needed)" -ForegroundColor Yellow
            return $true  # Not a critical failure
        }
    }
    catch {
        Write-Host "• Error testing strong name bypass: $($_.Exception.Message)" -ForegroundColor Yellow
        return $true  # Not a critical failure
    }
}

# Function to test system compatibility
function Test-SystemCompatibility {
    Write-Host "=== Testing System Compatibility ===" -ForegroundColor Magenta
    
    try {
        # Check Windows version
        $osInfo = Get-CimInstance -ClassName Win32_OperatingSystem
        Write-Host "✓ Operating System: $($osInfo.Caption) (Build $($osInfo.BuildNumber))" -ForegroundColor Green
        
        # Check if running as Administrator
        $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
        $isAdmin = ([Security.Principal.WindowsPrincipal] $currentUser).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
        
        if ($isAdmin) {
            Write-Host "✓ Running with Administrator privileges" -ForegroundColor Green
        } else {
            Write-Host "• Not running as Administrator (required for deployment)" -ForegroundColor Yellow
        }
        
        # Check PowerShell version
        Write-Host "✓ PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor Green
        
        # Check available disk space
        $systemDrive = Get-CimInstance -ClassName Win32_LogicalDisk | Where-Object { $_.DeviceID -eq $env:SystemDrive }
        $freeSpaceGB = [math]::Round($systemDrive.FreeSpace / 1GB, 2)
        
        if ($freeSpaceGB -gt 1) {
            Write-Host "✓ Available disk space on system drive: $freeSpaceGB GB" -ForegroundColor Green
        } else {
            Write-Host "⚠ Low disk space on system drive: $freeSpaceGB GB" -ForegroundColor Yellow
        }
        
        return $true
    }
    catch {
        Write-Host "✗ Error checking system compatibility: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to test AIMX binary availability
function Test-AimxBinaries {
    Write-Host "=== Testing AIMX Binary Availability ===" -ForegroundColor Magenta
    
    $binaryPath = ".\bin"
    $systemSubfolder = "system"
    $programDataSubfolder = "programdata"
    
    if (Test-Path $binaryPath) {
        Write-Host "✓ Binary path exists: $binaryPath" -ForegroundColor Green
        
        $systemSourcePath = Join-Path $binaryPath $systemSubfolder
        $programDataSourcePath = Join-Path $binaryPath $programDataSubfolder
        
        if (Test-Path $systemSourcePath) {
            Write-Host "✓ System subfolder exists: $systemSourcePath" -ForegroundColor Green
            
            # Check for key files
            $keySystemFiles = @("Aimx.ServiceInstaller.exe", "aimx.psd1", "netrag.exe")
            foreach ($file in $keySystemFiles) {
                $filePath = Join-Path $systemSourcePath $file
                if (Test-Path $filePath) {
                    Write-Host "  ✓ $file" -ForegroundColor Green
                } else {
                    Write-Host "  • $file (not found)" -ForegroundColor Gray
                }
            }
        } else {
            Write-Host "✗ System subfolder missing: $systemSourcePath" -ForegroundColor Red
        }
        
        if (Test-Path $programDataSourcePath) {
            Write-Host "✓ ProgramData subfolder exists: $programDataSourcePath" -ForegroundColor Green
        } else {
            Write-Host "✗ ProgramData subfolder missing: $programDataSourcePath" -ForegroundColor Red
        }
        
        return (Test-Path $systemSourcePath) -and (Test-Path $programDataSourcePath)
    } else {
        Write-Host "✗ Binary path does not exist: $binaryPath" -ForegroundColor Red
        return $false
    }
}

# Main execution
Write-Host "=== AIMX Prerequisites Test ===" -ForegroundColor Green
Write-Host "Testing AIMX deployment prerequisites..." -ForegroundColor Cyan
Write-Host ""

$allTestsPassed = $true

# Run all tests
$dotnetTest = Test-DotNetInstallation
Write-Host ""

$strongNameTest = Test-StrongNameBypass
Write-Host ""

$systemTest = Test-SystemCompatibility
Write-Host ""

$binaryTest = Test-AimxBinaries
Write-Host ""

# Determine overall result
$allTestsPassed = $dotnetTest -and $strongNameTest -and $systemTest -and $binaryTest

# Summary
Write-Host "=== Test Summary ===" -ForegroundColor Green
Write-Host ""

if ($allTestsPassed) {
    Write-Host "✓ All prerequisite tests passed! AIMX is ready for deployment." -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Run Deploy-AIMX.ps1 to deploy AIMX" -ForegroundColor Cyan
    Write-Host "2. Or run Pre-Deploy-AIMX.ps1 for complete deployment" -ForegroundColor Cyan
} else {
    Write-Host "⚠ Some prerequisite tests failed. Please review the output above." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Recommended actions:" -ForegroundColor Yellow
    Write-Host "1. Run Pre-Deploy-AIMX.ps1 to install missing prerequisites" -ForegroundColor Cyan
    Write-Host "2. Ensure you have Administrator privileges" -ForegroundColor Cyan
    Write-Host "3. Verify AIMX binaries are available in .\bin folder" -ForegroundColor Cyan
}
Write-Host ""
