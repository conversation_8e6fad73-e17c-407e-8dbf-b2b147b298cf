# AIMX Deployment Prerequisites

This document describes the prerequisite installation process for AIMX deployment.

## Overview

The AIMX deployment process has been split into two main scripts:

1. **Pre-Deploy-AIMX.ps1** - Handles prerequisites installation
2. **Deploy-AIMX.ps1** - Handles the main AIMX deployment

## Prerequisites Handled

### .NET 8.0 Runtime
- **Microsoft.NETCore.App 8.0** - Core .NET runtime
- **Microsoft.AspNetCore.App 8.0** - ASP.NET Core runtime

### Strong Name Verification Bypass
- Configures bypass for AIMX assemblies to prevent signature verification issues
- Primary assembly: aimxpsh.dll (PowerShell module)
- Additional assemblies: Aimx.ServiceInstaller.exe, netrag.exe (if present)

### System Requirements
- Windows operating system
- Administrator privileges
- PowerShell 5.1 or later
- Sufficient disk space

## Scripts

### Pre-Deploy-AIMX.ps1

**Purpose**: Installs all prerequisites required for AIMX deployment.

**Parameters**:
- `-SkipDotNetInstall` - Skip .NET runtime installation
- `-SkipStrongNameBypass` - Skip strong name verification bypass
- `-RunMainDeployment` - Automatically run Deploy-AIMX.ps1 after prerequisites (default: true)
- `-Force` - Skip confirmation prompts

**Examples**:
```powershell
# Complete deployment (prerequisites + main deployment)
.\Pre-Deploy-AIMX.ps1

# Install prerequisites only
.\Pre-Deploy-AIMX.ps1 -RunMainDeployment:$false

# Skip .NET installation but configure strong name bypass
.\Pre-Deploy-AIMX.ps1 -SkipDotNetInstall

# Silent installation
.\Pre-Deploy-AIMX.ps1 -Force
```

### Deploy-AIMX.ps1

**Purpose**: Handles the main AIMX deployment (services, files, configuration).

**Note**: This script assumes prerequisites are already installed. Run Pre-Deploy-AIMX.ps1 first.

**Examples**:
```powershell
# Main deployment (after prerequisites)
.\Deploy-AIMX.ps1

# Force deployment without prompts
.\Deploy-AIMX.ps1 -Force
```

### Test-Prerequisites.ps1

**Purpose**: Tests and validates that prerequisites are properly installed.

**Examples**:
```powershell
# Test all prerequisites
.\Test-Prerequisites.ps1
```

## Deployment Process

### Option 1: Complete Deployment (Recommended)
```powershell
# Run as Administrator
.\Pre-Deploy-AIMX.ps1
```
This will:
1. Install .NET 8.0 Runtime if needed
2. Configure strong name verification bypass
3. Automatically run Deploy-AIMX.ps1

### Option 2: Step-by-Step Deployment
```powershell
# Step 1: Install prerequisites
.\Pre-Deploy-AIMX.ps1 -RunMainDeployment:$false

# Step 2: Test prerequisites (optional)
.\Test-Prerequisites.ps1

# Step 3: Run main deployment
.\Deploy-AIMX.ps1
```

### Option 3: Prerequisites Only
```powershell
# Install only prerequisites
.\Pre-Deploy-AIMX.ps1 -RunMainDeployment:$false

# Test installation
.\Test-Prerequisites.ps1
```

## Troubleshooting

### .NET Installation Issues

**Problem**: .NET 8.0 installation fails
**Solutions**:
1. Ensure Administrator privileges
2. Verify local installers exist in `dotnet` folder
3. Check that `dotnet-runtime-8.0.18-win-x64.exe` and `aspnetcore-runtime-8.0.18-win-x64.exe` are present
4. Use `-SkipDotNetInstall` if .NET is already installed

**Local .NET Installers**:
- Located in: `dotnet/dotnet-runtime-8.0.18-win-x64.exe`
- Located in: `dotnet/aspnetcore-runtime-8.0.18-win-x64.exe`
- No internet connection required

### Strong Name Bypass Issues

**Problem**: Strong name verification bypass fails
**Solutions**:
1. Ensure Administrator privileges
2. Verify `sn.exe` exists in `dotnet` folder
3. Check that `dotnet/sn.exe` is accessible
4. Use `-SkipStrongNameBypass` if not needed
5. AIMX may still work without bypass

**Local sn.exe**:
- Located in: `dotnet/sn.exe`
- Used for strong name verification bypass
- No .NET SDK installation required

### Permission Issues

**Problem**: Access denied errors
**Solutions**:
1. Run PowerShell as Administrator
2. Check execution policy: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`
3. Ensure user has local admin rights

### Binary Path Issues

**Problem**: Binary files not found
**Solutions**:
1. Ensure `.\bin` folder exists with `system` and `programdata` subfolders
2. Verify AIMX binaries are properly extracted
3. Check file paths in error messages

## File Structure

```
AIMX-Deployment/
├── Pre-Deploy-AIMX.ps1          # Prerequisites installation
├── Deploy-AIMX.ps1              # Main deployment
├── Test-Prerequisites.ps1       # Prerequisites testing
├── README-Prerequisites.md      # This documentation
├── dotnet/                      # Local .NET installers and tools
│   ├── dotnet-runtime-8.0.18-win-x64.exe
│   ├── aspnetcore-runtime-8.0.18-win-x64.exe
│   ├── sn.exe                   # Strong name tool
│   └── sn.exe.config
└── bin/                         # AIMX binaries
    ├── system/                  # System32 files
    │   ├── Aimx.ServiceInstaller.exe
    │   ├── aimx.psd1
    │   └── netrag.exe
    └── programdata/             # ProgramData files
        └── NetRagService/
            ├── NetRagService.exe
            ├── appsettings.json
            └── ...
```

## Security Considerations

### Administrator Privileges
- Required for system file installation
- Required for service installation
- Required for strong name bypass configuration

### Strong Name Verification Bypass
- Only configured for AIMX assemblies
- Does not affect system security
- Can be skipped if not needed

### .NET Runtime Installation
- Downloads from official Microsoft servers
- Uses silent installation for automation
- Temporary files are cleaned up after installation

## Support

For issues with prerequisite installation:

1. Run `Test-Prerequisites.ps1` to identify specific problems
2. Check the detailed error messages in the script output
3. Ensure all requirements are met (Administrator privileges, internet access)
4. Consider manual installation of .NET 8.0 if automatic installation fails

For AIMX-specific deployment issues, refer to the main AIMX documentation.
