#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Test script to verify AIMX assembly strong name bypass

.DESCRIPTION
    Tests the strong name bypass functionality for the actual aimxpsh.dll assembly

#>

# Test strong name bypass for aimxpsh.dll
Write-Host "=== Testing AIMX Assembly Strong Name Bypass ===" -ForegroundColor Green
Write-Host ""

# Check if aimxpsh.dll exists
$assemblyPath = ".\bin\system\aimxpsh.dll"
if (-not (Test-Path $assemblyPath)) {
    Write-Host "ERROR: aimxpsh.dll not found at: $assemblyPath" -ForegroundColor Red
    exit 1
}

Write-Host "✓ Found aimxpsh.dll at: $assemblyPath" -ForegroundColor Green

# Check if local sn.exe exists
$snExePath = ".\dotnet\sn.exe"
if (-not (Test-Path $snExePath)) {
    Write-Host "ERROR: sn.exe not found at: $snExePath" -ForegroundColor Red
    exit 1
}

Write-Host "✓ Found sn.exe at: $snExePath" -ForegroundColor Green
Write-Host ""

# Test strong name bypass configuration
Write-Host "Testing strong name bypass configuration..." -ForegroundColor Yellow

try {
    # Configure bypass for aimxpsh.dll
    $snResult = & $snExePath -Vr $assemblyPath 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Strong name verification bypass configured successfully for aimxpsh.dll" -ForegroundColor Green
    } else {
        Write-Host "Note: Strong name bypass result: $snResult" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "ERROR: Failed to configure strong name bypass" -ForegroundColor Red
}

Write-Host ""

# List current verification skip entries
Write-Host "Current strong name verification skip list:" -ForegroundColor Yellow
try {
    $skipList = & $snExePath -Vl 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host $skipList -ForegroundColor Gray

        # Check if our assembly is in the skip list
        if ($skipList -match "aimxpsh") {
            Write-Host "✓ aimxpsh.dll is in the verification skip list" -ForegroundColor Green
        } else {
            Write-Host "• aimxpsh.dll not found in verification skip list" -ForegroundColor Yellow
        }
    } else {
        Write-Host "Could not retrieve skip list: $skipList" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "ERROR: Failed to retrieve skip list" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Green
