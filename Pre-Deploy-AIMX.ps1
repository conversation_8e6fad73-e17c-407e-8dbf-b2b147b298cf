#!/usr/bin/env pwsh
<#
.SYNOPSIS
    AIMX prerequisite installation script that handles .NET runtime and strong name verification bypass

.DESCRIPTION
    This script handles the prerequisite installation for AIMX deployment:
    1. Checks and installs .NET 8.0 Runtime if needed
    2. Configures strong name verification bypass for AIMX assemblies
    3. Validates system requirements
    4. Optionally runs the main deployment script (Deploy-AIMX.ps1)

    This script must be run with Administrator privileges and should be executed
    before running Deploy-AIMX.ps1.

.PARAMETER SkipDotNetInstall
    Skip .NET runtime installation check and installation



.PARAMETER RunMainDeployment
    Automatically run Deploy-AIMX.ps1 after prerequisites are installed

.PARAMETER Force
    Skip confirmation prompts

.EXAMPLE
    .\Pre-Deploy-AIMX.ps1
    
    # Install prerequisites only
    .\Pre-Deploy-AIMX.ps1 -RunMainDeployment:$false

    # Skip .NET installation
    .\Pre-Deploy-AIMX.ps1 -SkipDotNetInstall

.NOTES
    This script requires Administrator privileges. Right-click PowerShell and "Run as Administrator".
    
    Prerequisites handled:
    - .NET 8.0 Runtime (Desktop and ASP.NET Core)
    - System compatibility checks

    Note: Strong name verification bypass is handled by Deploy-AIMX.ps1 after files are copied.

#>

[CmdletBinding()]
param(
    [switch]$SkipDotNetInstall,

    [bool]$RunMainDeployment = $true,
    [switch]$Force
)

# Requires Administrator privileges
#Requires -RunAsAdministrator

# Function to get current user context
function Get-CurrentUserContext {
    try {
        $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
        return @{
            Name = $currentUser.Name
            IsSystem = $currentUser.IsSystem
            IsAdmin = ([Security.Principal.WindowsPrincipal] $currentUser).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
        }
    }
    catch {
        return @{
            Name = "Unknown"
            IsSystem = $false
            IsAdmin = $false
        }
    }
}

# Function to check if .NET 8.0 is installed
function Test-DotNet8Installed {
    try {
        # Check for .NET 8.0 runtime
        $dotnetInfo = & dotnet --list-runtimes 2>$null
        if ($LASTEXITCODE -eq 0) {
            $net8Runtime = $dotnetInfo | Where-Object { $_ -match "Microsoft\.NETCore\.App 8\." }
            $aspNet8Runtime = $dotnetInfo | Where-Object { $_ -match "Microsoft\.AspNetCore\.App 8\." }
            
            return @{
                DotNetInstalled = $true
                Net8CoreInstalled = $null -ne $net8Runtime
                Net8AspNetInstalled = $null -ne $aspNet8Runtime
                Runtimes = $dotnetInfo
            }
        }
    }
    catch {
        # dotnet command not found or failed
    }
    
    return @{
        DotNetInstalled = $false
        Net8CoreInstalled = $false
        Net8AspNetInstalled = $false
        Runtimes = @()
    }
}

# Function to install .NET 8.0 Runtime from local installers
function Install-DotNet8Runtime {
    Write-Host "Installing .NET 8.0 Runtime from local installers..." -ForegroundColor Yellow

    # Local installer paths
    $dotnetFolder = Join-Path $PSScriptRoot "dotnet"
    $net8RuntimeInstaller = Join-Path $dotnetFolder "dotnet-runtime-8.0.18-win-x64.exe"
    $aspNet8RuntimeInstaller = Join-Path $dotnetFolder "aspnetcore-runtime-8.0.18-win-x64.exe"

    # Verify local installers exist
    if (-not (Test-Path $dotnetFolder)) {
        Write-Host "ERROR: dotnet folder not found at: $dotnetFolder" -ForegroundColor Red
        return $false
    }

    if (-not (Test-Path $net8RuntimeInstaller)) {
        Write-Host "ERROR: .NET 8.0 Runtime installer not found at: $net8RuntimeInstaller" -ForegroundColor Red
        return $false
    }

    if (-not (Test-Path $aspNet8RuntimeInstaller)) {
        Write-Host "ERROR: ASP.NET Core 8.0 Runtime installer not found at: $aspNet8RuntimeInstaller" -ForegroundColor Red
        return $false
    }

    try {
        Write-Host "Found local .NET installers:" -ForegroundColor Green
        Write-Host "  .NET Runtime: $net8RuntimeInstaller" -ForegroundColor Cyan
        Write-Host "  ASP.NET Core Runtime: $aspNet8RuntimeInstaller" -ForegroundColor Cyan
        Write-Host ""

        # Install .NET 8.0 Runtime
        Write-Host "Installing .NET 8.0 Runtime (this may take a few minutes)..." -ForegroundColor Cyan
        $installResult = Start-Process -FilePath $net8RuntimeInstaller -ArgumentList "/quiet" -Wait -PassThru
        if ($installResult.ExitCode -ne 0) {
            Write-Host "WARNING: .NET 8.0 Runtime installation may have failed (exit code: $($installResult.ExitCode))" -ForegroundColor Yellow
        } else {
            Write-Host ".NET 8.0 Runtime installed successfully." -ForegroundColor Green
        }

        # Install ASP.NET Core 8.0 Runtime
        Write-Host "Installing ASP.NET Core 8.0 Runtime (this may take a few minutes)..." -ForegroundColor Cyan
        $aspInstallResult = Start-Process -FilePath $aspNet8RuntimeInstaller -ArgumentList "/quiet" -Wait -PassThru
        if ($aspInstallResult.ExitCode -ne 0) {
            Write-Host "WARNING: ASP.NET Core 8.0 Runtime installation may have failed (exit code: $($aspInstallResult.ExitCode))" -ForegroundColor Yellow
        } else {
            Write-Host "ASP.NET Core 8.0 Runtime installed successfully." -ForegroundColor Green
        }

        return $true
    }
    catch {
        Write-Host "ERROR: Failed to install .NET 8.0 Runtime" -ForegroundColor Red
        return $false
    }
}



# Get current execution context
$userContext = Get-CurrentUserContext

Write-Host "=== AIMX Prerequisites Installation Script ===" -ForegroundColor Green
Write-Host "Installing prerequisites for AIMX deployment..." -ForegroundColor Cyan
Write-Host ""
Write-Host "Execution Context:" -ForegroundColor Yellow
Write-Host "  User: $($userContext.Name)" -ForegroundColor Cyan
Write-Host "  Is Admin: $($userContext.IsAdmin)" -ForegroundColor Cyan
Write-Host ""

# Confirmation prompt
if (-not $Force) {
    Write-Host "This script will install the following prerequisites:" -ForegroundColor Yellow
    if (-not $SkipDotNetInstall) {
        Write-Host "  - .NET 8.0 Runtime (if not already installed)" -ForegroundColor Cyan
    }
    Write-Host "  - System compatibility validation" -ForegroundColor Cyan
    Write-Host ""
    
    $confirmation = Read-Host "Proceed with prerequisites installation? (y/N)"
    if ($confirmation -notmatch '^[Yy]') {
        Write-Host "Prerequisites installation cancelled." -ForegroundColor Yellow
        exit 0
    }
    Write-Host ""
}

$prerequisitesSuccess = $true

# Step 1: Check and install .NET 8.0 Runtime
if (-not $SkipDotNetInstall) {
    Write-Host "=== Step 1: .NET 8.0 Runtime Check ===" -ForegroundColor Magenta
    
    $dotnetStatus = Test-DotNet8Installed
    
    if ($dotnetStatus.DotNetInstalled) {
        Write-Host ".NET is installed. Checking for .NET 8.0..." -ForegroundColor Green
        Write-Host "  .NET 8.0 Core Runtime: $(if ($dotnetStatus.Net8CoreInstalled) { 'Installed' } else { 'Missing' })" -ForegroundColor $(if ($dotnetStatus.Net8CoreInstalled) { 'Green' } else { 'Yellow' })
        Write-Host "  ASP.NET Core 8.0 Runtime: $(if ($dotnetStatus.Net8AspNetInstalled) { 'Installed' } else { 'Missing' })" -ForegroundColor $(if ($dotnetStatus.Net8AspNetInstalled) { 'Green' } else { 'Yellow' })
        
        if (-not $dotnetStatus.Net8CoreInstalled -or -not $dotnetStatus.Net8AspNetInstalled) {
            Write-Host "Installing missing .NET 8.0 components..." -ForegroundColor Yellow
            $installSuccess = Install-DotNet8Runtime
            if (-not $installSuccess) {
                $prerequisitesSuccess = $false
            }
        } else {
            Write-Host ".NET 8.0 Runtime is already installed." -ForegroundColor Green
        }
    } else {
        Write-Host ".NET is not installed or not accessible. Installing .NET 8.0 Runtime..." -ForegroundColor Yellow
        $installSuccess = Install-DotNet8Runtime
        if (-not $installSuccess) {
            $prerequisitesSuccess = $false
        }
    }
    Write-Host ""
} else {
    Write-Host "=== Step 1: .NET 8.0 Runtime Check (Skipped) ===" -ForegroundColor Magenta
    Write-Host "Skipping .NET runtime installation as requested." -ForegroundColor Yellow
    Write-Host ""
}



# Summary
Write-Host "=== Prerequisites Installation Summary ===" -ForegroundColor Green
Write-Host ""
if ($prerequisitesSuccess) {
    Write-Host "Prerequisites installation completed successfully!" -ForegroundColor Green
} else {
    Write-Host "Prerequisites installation completed with some errors. Please review the output above." -ForegroundColor Yellow
}
Write-Host ""

# Run main deployment if requested
if ($RunMainDeployment) {
    $deployScriptPath = Join-Path $PSScriptRoot "Deploy-AIMX.ps1"
    if (Test-Path $deployScriptPath) {
        Write-Host "=== Running Main Deployment ===" -ForegroundColor Green
        Write-Host "Executing Deploy-AIMX.ps1..." -ForegroundColor Cyan
        Write-Host ""
        
        try {
            & $deployScriptPath
        }
        catch {
            Write-Host "ERROR: Failed to run main deployment script" -ForegroundColor Red
        }
    } else {
        Write-Host "WARNING: Deploy-AIMX.ps1 not found at: $deployScriptPath" -ForegroundColor Yellow
        Write-Host "Please run Deploy-AIMX.ps1 manually after prerequisites are installed." -ForegroundColor Yellow
    }
} else {
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Run Deploy-AIMX.ps1 to complete the AIMX deployment" -ForegroundColor Cyan
    Write-Host "2. Verify the AIMX service is running: Get-Service AIMXSrv" -ForegroundColor Cyan
}
Write-Host ""
